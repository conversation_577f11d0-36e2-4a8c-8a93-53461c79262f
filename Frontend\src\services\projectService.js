import { api } from "../lib/api"

// Project services
export const projectService = {
  // Get projects assigned to the current employee
  getAssignedProjects: async () => {
    try {
      const response = await api.get("/project/assigned")
      return response.data
    } catch (error) {
      throw error.response?.data || error
    }
  },

  // Get project by ID
  getProjectById: async (projectId) => {
    try {
      const response = await api.get(`/project/${projectId}`)
      return response.data
    } catch (error) {
      throw error.response?.data || error
    }
  },
}

// Project Task services
export const projectTaskService = {
  // Create a new project task
  createTask: async (taskData) => {
    try {
      const response = await api.post("/project-task/create", taskData)
      return response.data
    } catch (error) {
      throw error.response?.data || error
    }
  },

  // Get all tasks for the current employee
  getMyTasks: async (params = {}) => {
    try {
      const response = await api.get("/project-task/my-tasks", { params })
      return response.data
    } catch (error) {
      throw error.response?.data || error
    }
  },

  // Get tasks for a specific project
  getProjectTasks: async (projectId, params = {}) => {
    try {
      const response = await api.get(`/project-task/project/${projectId}`, { params })
      return response.data
    } catch (error) {
      throw error.response?.data || error
    }
  },

  // Get task by ID
  getTaskById: async (taskId) => {
    try {
      const response = await api.get(`/project-task/${taskId}`)
      return response.data
    } catch (error) {
      throw error.response?.data || error
    }
  },

  // Update a task
  updateTask: async (taskId, updateData) => {
    try {
      const response = await api.put(`/project-task/${taskId}`, updateData)
      return response.data
    } catch (error) {
      throw error.response?.data || error
    }
  },

  // Delete a task
  deleteTask: async (taskId) => {
    try {
      const response = await api.delete(`/project-task/${taskId}`)
      return response.data
    } catch (error) {
      throw error.response?.data || error
    }
  },

  // Add subtask
  addSubtask: async (taskId, subtaskData) => {
    try {
      const response = await api.post(`/project-task/${taskId}/subtasks`, subtaskData)
      return response.data
    } catch (error) {
      throw error.response?.data || error
    }
  },

  // Update subtask
  updateSubtask: async (taskId, subtaskId, updateData) => {
    try {
      const response = await api.put(`/project-task/${taskId}/subtasks/${subtaskId}`, updateData)
      return response.data
    } catch (error) {
      throw error.response?.data || error
    }
  },

  // Delete subtask
  deleteSubtask: async (taskId, subtaskId) => {
    try {
      const response = await api.delete(`/project-task/${taskId}/subtasks/${subtaskId}`)
      return response.data
    } catch (error) {
      throw error.response?.data || error
    }
  },
}
