import mongoose from 'mongoose';
import bcryptjs from 'bcryptjs';
import dotenv from 'dotenv';
import Employee from './models/Employee.js';

dotenv.config();

async function verifyEmployeePassword() {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    
    const employee = await Employee.findOne({ username: 'testemployee_project' });
    if (!employee) {
      console.log('❌ Test employee not found');
      process.exit(1);
    }

    console.log('✅ Found employee:', employee.name);
    console.log('   Username:', employee.username);
    console.log('   Email verified:', employee.isEmailVerified);
    
    // Test password
    const testPassword = 'TestEmployee123!';
    const isValid = await bcryptjs.compare(testPassword, employee.password);
    
    console.log(`   Password "${testPassword}" is valid:`, isValid);
    
    if (!isValid) {
      console.log('❌ Password verification failed. Updating password...');
      const newHashedPassword = await bcryptjs.hash(testPassword, 10);
      employee.password = newHashedPassword;
      await employee.save();
      console.log('✅ Password updated successfully');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

verifyEmployeePassword();
