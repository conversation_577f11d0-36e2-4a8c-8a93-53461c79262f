import axios from 'axios';

const API_BASE = 'http://localhost:5000/api';

async function testServer() {
  try {
    console.log('🔐 Testing Employee Login...');
    
    // Login as employee
    const loginResponse = await axios.post(`${API_BASE}/auth/employee/login`, {
      username: 'anuk',
      password: 'anuk123'
    });
    
    console.log('✅ Employee logged in successfully');
    const token = loginResponse.data.token;
    const userId = loginResponse.data.user.id;
    
    // Test employee profile endpoint (this worked before)
    console.log('👤 Testing employee profile endpoint...');
    const profileResponse = await axios.get(`${API_BASE}/employee/${userId}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('✅ Employee profile fetched successfully!');
    console.log('Profile:', profileResponse.data.employee.name);
    
    // Test project assignment endpoint (this also worked)
    console.log('📋 Testing project assignment endpoint...');
    const projectsResponse = await axios.get(`${API_BASE}/project/assigned`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('✅ Projects fetched successfully!');
    console.log('Projects count:', projectsResponse.data.count);
    
    // Test the simple create route first
    console.log('🧪 Testing simple create-test endpoint...');
    const projectId = projectsResponse.data.data[0]._id;

    const taskData = {
      name: 'Debug Test Task',
      description: 'A debug test task',
      projectId: projectId,
      estimatedHours: 1,
      estimatedMinutes: 0
    };

    const simpleResponse = await axios.post(`${API_BASE}/project-task/create-test`, taskData, {
      headers: { Authorization: `Bearer ${token}` }
    });

    console.log('✅ Simple create test works!');
    console.log('Response:', simpleResponse.data);

    // Now test the problematic project-task endpoint
    console.log('🚨 Testing problematic project-task endpoint...');

    const taskResponse = await axios.post(`${API_BASE}/project-task/create`, taskData, {
      headers: { Authorization: `Bearer ${token}` }
    });

    console.log('✅ Task created successfully!');
    console.log('Task:', taskResponse.data.data);
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response:', error.response.data);
    }
  }
}

testServer();
