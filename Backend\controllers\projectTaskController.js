import ProjectTask from "../models/ProjectTask.js"
import Project from "../models/Project.js"

// Helper function to check if employee is assigned to project
const checkProjectAssignment = async (projectId, employeeId) => {
  const project = await Project.findById(projectId)
  if (!project) {
    throw new Error("Project not found")
  }
  
  const isAssigned = project.assignment.some(
    assignment => assignment.employee.toString() === employeeId
  )
  
  if (!isAssigned) {
    throw new Error("You are not assigned to this project")
  }
  
  return project
}

// Create a new project task
export const createProjectTask = async (req, res) => {
  try {
    const { name, description, projectId, estimatedHours, estimatedMinutes, subtasks } = req.body

    // Validate required fields
    if (!name || !projectId) {
      return res.status(400).json({
        success: false,
        message: "Task name and project ID are required",
      })
    }

    // Check if employee is assigned to the project
    await checkProjectAssignment(projectId, req.user.id)

    const projectTask = new ProjectTask({
      name,
      description,
      project: projectId,
      createdBy: req.user.id,
      estimatedHours: estimatedHours || 0,
      estimatedMinutes: estimatedMinutes || 0,
    })

    // Add subtasks if provided
    if (subtasks && Array.isArray(subtasks)) {
      subtasks.forEach(subtask => {
        projectTask.subtasks.push({
          ...subtask,
          createdBy: req.user.id,
        })
      })
    }

    await projectTask.save()
    await projectTask.populate([
      { path: "project", select: "name description projectType" },
      { path: "createdBy", select: "name email username" },
    ])

    res.status(201).json({
      success: true,
      message: "Project task created successfully",
      data: projectTask,
    })
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message,
    })
  }
}

// Get all project tasks for a specific project
export const getProjectTasks = async (req, res) => {
  try {
    const { projectId } = req.params
    const { status, page = 1, limit = 10 } = req.query

    // Check if employee is assigned to the project
    await checkProjectAssignment(projectId, req.user.id)

    // Build filter
    const filter = {
      project: projectId,
      isActive: true,
    }

    if (status) {
      filter.status = status
    }

    const skip = (page - 1) * limit
    const tasks = await ProjectTask.find(filter)
      .populate("createdBy", "name email username")
      .populate("project", "name description")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))

    const total = await ProjectTask.countDocuments(filter)

    res.json({
      success: true,
      data: tasks,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalTasks: total,
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    })
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message,
    })
  }
}

// Get all tasks created by the current employee across all assigned projects
export const getMyProjectTasks = async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query

    // Get all projects assigned to the employee
    const assignedProjects = await Project.find({
      "assignment.employee": req.user.id,
      isActive: true,
    }).select("_id")

    const projectIds = assignedProjects.map(project => project._id)

    // Build filter
    const filter = {
      project: { $in: projectIds },
      isActive: true,
    }

    if (status) {
      filter.status = status
    }

    const skip = (page - 1) * limit
    const tasks = await ProjectTask.find(filter)
      .populate("createdBy", "name email username")
      .populate("project", "name description projectType")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))

    const total = await ProjectTask.countDocuments(filter)

    res.json({
      success: true,
      data: tasks,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalTasks: total,
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    })
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    })
  }
}

// Get a specific project task by ID
export const getProjectTaskById = async (req, res) => {
  try {
    const { taskId } = req.params

    const task = await ProjectTask.findById(taskId)
      .populate("createdBy", "name email username")
      .populate("project", "name description projectType")
      .populate("subtasks.createdBy", "name email username")

    if (!task) {
      return res.status(404).json({
        success: false,
        message: "Task not found",
      })
    }

    // Check if employee is assigned to the project
    await checkProjectAssignment(task.project._id, req.user.id)

    res.json({
      success: true,
      data: task,
    })
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message,
    })
  }
}

// Update a project task
export const updateProjectTask = async (req, res) => {
  try {
    const { taskId } = req.params
    const updates = req.body

    const task = await ProjectTask.findById(taskId)
    if (!task) {
      return res.status(404).json({
        success: false,
        message: "Task not found",
      })
    }

    // Check if employee is assigned to the project
    await checkProjectAssignment(task.project, req.user.id)

    // Update task fields
    const allowedUpdates = ["name", "description", "status", "estimatedHours", "estimatedMinutes"]
    allowedUpdates.forEach(field => {
      if (updates[field] !== undefined) {
        task[field] = updates[field]
      }
    })

    // Handle status change to completed
    if (updates.status === "completed") {
      await task.markCompleted()
    } else {
      await task.save()
    }

    await task.populate([
      { path: "createdBy", select: "name email username" },
      { path: "project", select: "name description" },
    ])

    res.json({
      success: true,
      message: "Task updated successfully",
      data: task,
    })
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message,
    })
  }
}

// Delete a project task
export const deleteProjectTask = async (req, res) => {
  try {
    const { taskId } = req.params

    const task = await ProjectTask.findById(taskId)
    if (!task) {
      return res.status(404).json({
        success: false,
        message: "Task not found",
      })
    }

    // Check if employee is assigned to the project
    await checkProjectAssignment(task.project, req.user.id)

    // Only the creator can delete the task
    if (task.createdBy.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: "You can only delete tasks you created",
      })
    }

    // Soft delete
    task.isActive = false
    await task.save()

    res.json({
      success: true,
      message: "Task deleted successfully",
    })
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message,
    })
  }
}

// Add a subtask to a project task
export const addSubtask = async (req, res) => {
  try {
    const { taskId } = req.params
    const { name, description, estimatedHours, estimatedMinutes } = req.body

    if (!name) {
      return res.status(400).json({
        success: false,
        message: "Subtask name is required",
      })
    }

    const task = await ProjectTask.findById(taskId)
    if (!task) {
      return res.status(404).json({
        success: false,
        message: "Task not found",
      })
    }

    // Check if employee is assigned to the project
    await checkProjectAssignment(task.project, req.user.id)

    await task.addSubtask({
      name,
      description,
      estimatedHours: estimatedHours || 0,
      estimatedMinutes: estimatedMinutes || 0,
    }, req.user.id)

    await task.populate([
      { path: "createdBy", select: "name email username" },
      { path: "project", select: "name description" },
      { path: "subtasks.createdBy", select: "name email username" },
    ])

    res.json({
      success: true,
      message: "Subtask added successfully",
      data: task,
    })
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message,
    })
  }
}

// Update a subtask
export const updateSubtask = async (req, res) => {
  try {
    const { taskId, subtaskId } = req.params
    const updates = req.body

    const task = await ProjectTask.findById(taskId)
    if (!task) {
      return res.status(404).json({
        success: false,
        message: "Task not found",
      })
    }

    // Check if employee is assigned to the project
    await checkProjectAssignment(task.project, req.user.id)

    await task.updateSubtask(subtaskId, updates)

    await task.populate([
      { path: "createdBy", select: "name email username" },
      { path: "project", select: "name description" },
      { path: "subtasks.createdBy", select: "name email username" },
    ])

    res.json({
      success: true,
      message: "Subtask updated successfully",
      data: task,
    })
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message,
    })
  }
}

// Delete a subtask
export const deleteSubtask = async (req, res) => {
  try {
    const { taskId, subtaskId } = req.params

    const task = await ProjectTask.findById(taskId)
    if (!task) {
      return res.status(404).json({
        success: false,
        message: "Task not found",
      })
    }

    // Check if employee is assigned to the project
    await checkProjectAssignment(task.project, req.user.id)

    await task.removeSubtask(subtaskId)

    await task.populate([
      { path: "createdBy", select: "name email username" },
      { path: "project", select: "name description" },
      { path: "subtasks.createdBy", select: "name email username" },
    ])

    res.json({
      success: true,
      message: "Subtask deleted successfully",
      data: task,
    })
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message,
    })
  }
}
