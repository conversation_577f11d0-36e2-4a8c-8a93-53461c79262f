import axios from 'axios';

const API_BASE = 'http://localhost:5000/api';

async function testEmployeeLogin() {
  console.log('🔐 Testing Employee Login...\n');

  try {
    console.log('Attempting login with:');
    console.log('Username: testemployee_project');
    console.log('Password: TestEmployee123!');
    
    const response = await axios.post(`${API_BASE}/auth/employee/login`, {
      username: 'testemployee_project',
      password: 'TestEmployee123!'
    });

    console.log('\n✅ Login successful!');
    console.log('Response:', response.data);
    
  } catch (error) {
    console.log('\n❌ Login failed!');
    console.log('Status:', error.response?.status);
    console.log('Error:', error.response?.data);
    
    // Try with different credentials
    console.log('\n🔄 Trying with existing employee "maryse"...');
    try {
      const response2 = await axios.post(`${API_BASE}/auth/employee/login`, {
        username: 'maryse',
        password: 'password123'
      });
      console.log('✅ Login with maryse successful!');
      console.log('Response:', response2.data);
    } catch (error2) {
      console.log('❌ Login with maryse also failed');
      console.log('Error:', error2.response?.data);
    }
  }
}

testEmployeeLogin();
