import axios from 'axios';

const API_BASE = 'http://localhost:5000/api';

async function testEmployeeLogin() {
  console.log('🔐 Testing Employee Login...\n');

  try {
    console.log('Attempting login with:');
    console.log('Username: anuk');
    console.log('Password: anuk123');
    
    const response = await axios.post(`${API_BASE}/auth/employee/login`, {
      username: 'anuk',
      password: 'anuk123'
    });

    console.log('\n✅ Login successful!');
    console.log('User:', response.data.user.name);
    console.log('Department:', response.data.user.department.name);

    // Test the project assignment endpoint
    console.log('\n🔍 Testing project assignment endpoint...');
    console.log('Token:', response.data.token.substring(0, 50) + '...');

    try {
      const projectsResponse = await axios.get(`${API_BASE}/project/assigned`, {
        headers: { Authorization: `Bearer ${response.data.token}` }
      });
      console.log('✅ Projects fetched successfully!');
      console.log('Projects count:', projectsResponse.data.count);
      console.log('Projects:', projectsResponse.data.data);
    } catch (projectError) {
      console.log('❌ Failed to fetch projects');
      console.log('Status:', projectError.response?.status);
      console.log('Error:', projectError.response?.data);

      // Let's also test a simple endpoint to verify token works
      console.log('\n🔍 Testing token with employee profile endpoint...');
      try {
        const profileResponse = await axios.get(`${API_BASE}/employee/${response.data.user.id}`, {
          headers: { Authorization: `Bearer ${response.data.token}` }
        });
        console.log('✅ Employee profile fetched successfully!');
        console.log('Profile:', profileResponse.data.employee.name);
      } catch (profileError) {
        console.log('❌ Failed to fetch employee profile');
        console.log('Status:', profileError.response?.status);
        console.log('Error:', profileError.response?.data);
      }
    }
    
  } catch (error) {
    console.log('\n❌ Login failed!');
    console.log('Status:', error.response?.status);
    console.log('Error:', error.response?.data);
    
    // Try with different credentials
    console.log('\n🔄 Trying with existing employee "maryse"...');
    try {
      const response2 = await axios.post(`${API_BASE}/auth/employee/login`, {
        username: 'maryse',
        password: 'password123'
      });
      console.log('✅ Login with maryse successful!');
      console.log('Response:', response2.data);
    } catch (error2) {
      console.log('❌ Login with maryse also failed');
      console.log('Error:', error2.response?.data);
    }
  }
}

testEmployeeLogin();
