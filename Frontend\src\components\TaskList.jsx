import { useState, useEffect } from "react"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "./ui/Card"
import { Button } from "./ui/Button"
import { Badge } from "./ui/Badge"
import { projectTaskService } from "../services/projectService"

export default function TaskList({ 
  project, 
  onTaskSelect, 
  onCreateTask, 
  onEditTask, 
  onDeleteTask,
  refreshTrigger = 0 
}) {
  const [tasks, setTasks] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchTasks = async () => {
      if (!project) return

      try {
        setLoading(true)
        const response = await projectTaskService.getProjectTasks(project._id)
        setTasks(response.data || [])
      } catch (err) {
        setError(err.message || "Failed to fetch tasks")
        console.error("Error fetching tasks:", err)
      } finally {
        setLoading(false)
      }
    }

    fetchTasks()
  }, [project, refreshTrigger])

  const getStatusColor = (status) => {
    switch (status) {
      case "completed": return "success"
      case "in progress": return "warning"
      case "to do": return "default"
      case "on hold": return "secondary"
      case "cancelled": return "destructive"
      default: return "default"
    }
  }

  const formatTime = (hours, minutes) => {
    if (hours === 0 && minutes === 0) return "No estimate"
    if (hours === 0) return `${minutes}m`
    if (minutes === 0) return `${hours}h`
    return `${hours}h ${minutes}m`
  }

  const handleDeleteTask = async (taskId) => {
    if (!window.confirm("Are you sure you want to delete this task?")) return

    try {
      await projectTaskService.deleteTask(taskId)
      setTasks(prev => prev.filter(task => task._id !== taskId))
      if (onDeleteTask) onDeleteTask(taskId)
    } catch (err) {
      console.error("Error deleting task:", err)
      alert("Failed to delete task. Please try again.")
    }
  }

  if (loading) {
    return (
      <Card variant="modern">
        <CardHeader>
          <CardTitle>Project Tasks</CardTitle>
          <CardDescription>Tasks for {project?.name}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card variant="modern">
        <CardHeader>
          <CardTitle>Project Tasks</CardTitle>
          <CardDescription>Tasks for {project?.name}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-500 dark:text-red-400">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-4"
              onClick={() => window.location.reload()}
            >
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card variant="modern">
      <CardHeader className="bg-gradient-to-r from-slate-50 to-indigo-50 dark:from-slate-800 dark:to-indigo-900/20 border-b border-slate-200 dark:border-slate-700">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle gradient className="text-2xl">Project Tasks</CardTitle>
            <CardDescription>
              Tasks for {project?.name} ({tasks.length} total)
            </CardDescription>
          </div>
          <Button 
            variant="primary" 
            size="sm"
            onClick={onCreateTask}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Task
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        {tasks.length > 0 ? (
          <div className="space-y-4">
            {tasks.map((task) => (
              <div 
                key={task._id} 
                className="group flex items-center justify-between p-4 bg-slate-50/50 dark:bg-slate-800/50 rounded-xl border border-slate-200/50 dark:border-slate-700/50 hover:bg-white dark:hover:bg-slate-800 hover:shadow-md transition-all duration-300 hover:scale-[1.02]"
              >
                <div className="flex items-center space-x-4 flex-1">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform shadow-lg">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-slate-900 dark:text-white group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors">
                      {task.name}
                    </h4>
                    <p className="text-sm text-slate-600 dark:text-slate-400 line-clamp-2">
                      {task.description || "No description provided"}
                    </p>
                    <div className="flex items-center space-x-4 mt-2">
                      <span className="text-xs text-slate-500 dark:text-slate-400">
                        Created: {new Date(task.createdAt).toLocaleDateString()}
                      </span>
                      <span className="text-xs text-slate-500 dark:text-slate-400">
                        Estimated: {formatTime(task.estimatedHours, task.estimatedMinutes)}
                      </span>
                      {task.subtasks?.length > 0 && (
                        <span className="text-xs text-slate-500 dark:text-slate-400">
                          Subtasks: {task.completedSubtasksCount}/{task.subtasks.length}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Badge variant={getStatusColor(task.status)} size="sm">
                    {task.status}
                  </Badge>
                  {task.progressPercentage !== undefined && (
                    <Badge variant="outline" size="sm">
                      {task.progressPercentage}%
                    </Badge>
                  )}
                  <div className="flex items-center space-x-1">
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => onTaskSelect && onTaskSelect(task)}
                      title="View Details"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => onEditTask && onEditTask(task)}
                      title="Edit Task"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleDeleteTask(task._id)}
                      className="text-red-500 hover:text-red-700"
                      title="Delete Task"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">No Tasks Yet</h3>
            <p className="text-slate-600 dark:text-slate-400 mb-4">
              Get started by creating your first task for this project.
            </p>
            <Button 
              variant="primary" 
              onClick={onCreateTask}
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Create First Task
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
