import axios from 'axios';

const API_BASE = 'http://localhost:5000/api';

async function testProjectTaskCreation() {
  try {
    console.log('🔐 Testing Employee Login...');
    
    // Login as employee
    const loginResponse = await axios.post(`${API_BASE}/auth/employee/login`, {
      username: 'anuk',
      password: 'anuk123'
    });
    
    console.log('✅ Employee logged in successfully');
    const token = loginResponse.data.token;
    
    // Get assigned projects
    console.log('📋 Getting assigned projects...');
    const projectsResponse = await axios.get(`${API_BASE}/project/assigned`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('✅ Projects retrieved:', projectsResponse.data.count);
    const projectId = projectsResponse.data.data[0]._id;
    console.log('Using project:', projectsResponse.data.data[0].name);
    
    // Create a simple project task
    console.log('✏️ Creating project task...');
    const taskData = {
      name: 'Simple Test Task',
      description: 'A simple test task',
      projectId: projectId,
      estimatedHours: 1,
      estimatedMinutes: 30
    };
    
    console.log('Task data:', taskData);
    
    const taskResponse = await axios.post(`${API_BASE}/project-task/create`, taskData, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('✅ Task created successfully!');
    console.log('Task:', taskResponse.data.data);
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response:', error.response.data);
    }
  }
}

testProjectTaskCreation();
