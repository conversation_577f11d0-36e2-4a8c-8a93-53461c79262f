import axios from 'axios';

const API_BASE = 'http://localhost:5000/api';

async function testRoute() {
  try {
    console.log('🧪 Testing project-task test route...');
    
    const response = await axios.get(`${API_BASE}/project-task/test`);
    
    console.log('✅ Test route works!');
    console.log('Response:', response.data);
    
  } catch (error) {
    console.log('❌ Error:', error.message);
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response:', error.response.data);
    }
  }
}

testRoute();
