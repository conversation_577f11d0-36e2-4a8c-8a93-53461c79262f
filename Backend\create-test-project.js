import mongoose from 'mongoose';
import dotenv from 'dotenv';
import Project from './models/Project.js';
import Employee from './models/Employee.js';
import Department from './models/Department.js';
import User from './models/User.js';

dotenv.config();

async function createTestProject() {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    
    // Get test admin
    const testAdmin = await User.findOne({ username: 'testadmin_project' }).populate('department');
    if (!testAdmin) {
      console.log('❌ Test admin not found. Please run create-test-admin.js first');
      process.exit(1);
    }

    // Get test employee
    const testEmployee = await Employee.findOne({ username: 'testemployee_project' }).populate('department');
    if (!testEmployee) {
      console.log('❌ Test employee not found. Please run create-test-employee.js first');
      process.exit(1);
    }

    // Check if test project already exists
    const existingProject = await Project.findOne({ name: 'Test Project for Task Management' });
    if (existingProject) {
      console.log('✅ Test project already exists: Test Project for Task Management');
      console.log(`   Project ID: ${existingProject._id}`);
      console.log(`   Assigned employees: ${existingProject.assignment.length}`);
      process.exit(0);
    }

    // Create test project
    const testProject = new Project({
      name: 'Test Project for Task Management',
      description: 'A test project to verify task management functionality for employees',
      projectType: 'public',
      priority: 'medium',
      assignment: [
        {
          employee: testEmployee._id,
          department: testEmployee.department._id
        }
      ],
      createdBy: testAdmin._id,
      department: testAdmin.department._id,
      status: 'active'
    });

    await testProject.save();
    await testProject.populate([
      { path: 'createdBy', select: 'name email' },
      { path: 'department', select: 'name' },
      { path: 'assignment.employee', select: 'name email username' },
      { path: 'assignment.department', select: 'name' }
    ]);

    console.log('✅ Created test project for task management:');
    console.log(`   Project Name: ${testProject.name}`);
    console.log(`   Project ID: ${testProject._id}`);
    console.log(`   Project Type: ${testProject.projectType}`);
    console.log(`   Created By: ${testProject.createdBy.name}`);
    console.log(`   Department: ${testProject.department.name}`);
    console.log('   Assigned Employees:');
    testProject.assignment.forEach(assignment => {
      console.log(`     - ${assignment.employee.name} (${assignment.employee.username}) from ${assignment.department.name}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

createTestProject();
