import express from "express"
import mongoose from "mongoose"
import cors from "cors"
import dotenv from "dotenv"
import path from "path"

import authRoutes from "./routes/authRoutes.js"
import adminRoutes from "./routes/adminRoutes.js"
import employeeRoutes from "./routes/employeeRoutes.js"
import departmentRoutes from "./routes/departmentRoutes.js"
import taskRoutes from "./routes/taskRoutes.js"
import projectRoutes from "./routes/projectRoutes.js"
import projectTaskRoutes from "./routes/projectTaskRoutes.js"
import testRoutes from "./routes/testRoutes.js"
import taskTypeRoutes from "./routes/taskTypeRoutes.js"

dotenv.config()

const app = express()

app.use(cors())
app.use(express.json())

app.use("/api/auth", authRoutes)
app.use("/api/admin", adminRoutes)
app.use("/api/employee", employeeRoutes)
app.use("/api/department", departmentRoutes)
app.use("/api/task", taskRoutes)
app.use("/api/project", projectRoutes)
app.use("/api/project-task", projectTaskRoutes)
app.use("/api/test", testRoutes)
app.use("/api/tasktype", taskTypeRoutes)

app.get("/", (req, res) => {
  res.send("TaskManager API is running")
})

mongoose.connect(process.env.MONGO_URI)
  .then(() => {
    console.log('MongoDB connected');
    app.listen(process.env.PORT || 5000, () => {
      console.log(`Server running on port ${process.env.PORT || 5000}`);
    });
  })
  .catch(err => console.log(err));
