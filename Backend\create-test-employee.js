import mongoose from 'mongoose';
import bcryptjs from 'bcryptjs';
import dotenv from 'dotenv';
import Employee from './models/Employee.js';
import Department from './models/Department.js';

dotenv.config();

async function createTestEmployee() {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    
    // Get HR department
    const hrDepartment = await Department.findOne({ name: 'HR' });
    if (!hrDepartment) {
      console.log('❌ HR department not found');
      process.exit(1);
    }

    // Check if test employee already exists
    const existingEmployee = await Employee.findOne({ username: 'testemployee_project' });
    if (existingEmployee) {
      console.log('✅ Test employee already exists: testemployee_project / TestEmployee123!');
      console.log(`   Department: ${hrDepartment.name}`);
      console.log(`   Email Verified: ${existingEmployee.isEmailVerified}`);
      process.exit(0);
    }

    // Hash password
    const hashedPassword = await bcryptjs.hash('TestEmployee123!', 10);

    // Create test employee
    const testEmployee = new Employee({
      name: 'Project Test Employee',
      email: '<EMAIL>',
      contactNumber: '+1234567890',
      address: '123 Test Street',
      username: 'testemployee_project',
      password: hashedPassword,
      department: hrDepartment._id,
      isEmailVerified: true // Skip email verification for testing
    });

    await testEmployee.save();
    console.log('✅ Created test employee for project testing:');
    console.log('   Username: testemployee_project');
    console.log('   Password: TestEmployee123!');
    console.log(`   Department: ${hrDepartment.name}`);
    console.log('   Email Verified: true');
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

createTestEmployee();
