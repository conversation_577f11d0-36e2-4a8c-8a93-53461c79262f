import mongoose from 'mongoose';
import Employee from './models/Employee.js';
import Department from './models/Department.js';
import dotenv from 'dotenv';

dotenv.config();

mongoose.connect(process.env.MONGO_URI).then(async () => {
  console.log('=== EMPLOYEES ===');
  const employees = await Employee.find().populate('department');
  employees.forEach(emp => {
    console.log(`- ${emp.name} (${emp.username}) - Department: ${emp.department?.name} - Email Verified: ${emp.isEmailVerified}`);
  });
  process.exit(0);
}).catch(err => {
  console.error('Error:', err);
  process.exit(1);
});
