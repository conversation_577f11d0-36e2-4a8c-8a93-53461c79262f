import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "./ui/Card"
import { Button } from "./ui/Button"
import { Badge } from "./ui/Badge"

export default function TaskForm({ 
  task = null, 
  project, 
  onSubmit, 
  onCancel, 
  loading = false 
}) {
  const [formData, setFormData] = useState({
    name: task?.name || "",
    description: task?.description || "",
    status: task?.status || "to do",
    estimatedHours: task?.estimatedHours || 0,
    estimatedMinutes: task?.estimatedMinutes || 0,
    subtasks: task?.subtasks || []
  })

  const [newSubtask, setNewSubtask] = useState({
    name: "",
    description: "",
    estimatedHours: 0,
    estimatedMinutes: 0
  })

  const statusOptions = [
    { value: "to do", label: "To Do", color: "default" },
    { value: "in progress", label: "In Progress", color: "warning" },
    { value: "on hold", label: "On Hold", color: "secondary" },
    { value: "completed", label: "Completed", color: "success" },
    { value: "cancelled", label: "Cancelled", color: "destructive" }
  ]

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubtaskChange = (field, value) => {
    setNewSubtask(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const addSubtask = () => {
    if (!newSubtask.name.trim()) return

    setFormData(prev => ({
      ...prev,
      subtasks: [...prev.subtasks, { ...newSubtask, id: Date.now() }]
    }))

    setNewSubtask({
      name: "",
      description: "",
      estimatedHours: 0,
      estimatedMinutes: 0
    })
  }

  const removeSubtask = (index) => {
    setFormData(prev => ({
      ...prev,
      subtasks: prev.subtasks.filter((_, i) => i !== index)
    }))
  }

  const updateSubtask = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      subtasks: prev.subtasks.map((subtask, i) => 
        i === index ? { ...subtask, [field]: value } : subtask
      )
    }))
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    if (!formData.name.trim()) return

    const submitData = {
      ...formData,
      projectId: project._id,
      subtasks: formData.subtasks.filter(subtask => subtask.name.trim())
    }

    onSubmit(submitData)
  }

  const getTotalEstimatedTime = () => {
    const taskTime = formData.estimatedHours * 60 + formData.estimatedMinutes
    const subtaskTime = formData.subtasks.reduce((total, subtask) => {
      return total + (subtask.estimatedHours * 60 + subtask.estimatedMinutes)
    }, 0)
    return taskTime + subtaskTime
  }

  const formatTime = (minutes) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours === 0) return `${mins}m`
    if (mins === 0) return `${hours}h`
    return `${hours}h ${mins}m`
  }

  return (
    <Card variant="modern" className="max-w-4xl mx-auto">
      <CardHeader className="bg-gradient-to-r from-slate-50 to-indigo-50 dark:from-slate-800 dark:to-indigo-900/20 border-b border-slate-200 dark:border-slate-700">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle gradient className="text-2xl">
              {task ? "Edit Task" : "Create New Task"}
            </CardTitle>
            <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
              Project: {project.name}
            </p>
          </div>
          <Badge variant="outline" size="sm">
            Total: {formatTime(getTotalEstimatedTime())}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Task Name */}
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Task Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-800 text-slate-900 dark:text-white"
              placeholder="Enter task name"
              required
            />
          </div>

          {/* Task Description */}
          <div>
            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-800 text-slate-900 dark:text-white"
              placeholder="Enter task description"
            />
          </div>

          {/* Status and Estimated Time */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange("status", e.target.value)}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-800 text-slate-900 dark:text-white"
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                Estimated Hours
              </label>
              <input
                type="number"
                min="0"
                value={formData.estimatedHours}
                onChange={(e) => handleInputChange("estimatedHours", parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-800 text-slate-900 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                Estimated Minutes
              </label>
              <input
                type="number"
                min="0"
                max="59"
                value={formData.estimatedMinutes}
                onChange={(e) => handleInputChange("estimatedMinutes", parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-800 text-slate-900 dark:text-white"
              />
            </div>
          </div>

          {/* Subtasks Section */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                Subtasks ({formData.subtasks.length})
              </h3>
              <Badge variant="outline" size="sm">
                Optional
              </Badge>
            </div>

            {/* Existing Subtasks */}
            {formData.subtasks.length > 0 && (
              <div className="space-y-3 mb-4">
                {formData.subtasks.map((subtask, index) => (
                  <div key={index} className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-2">
                        <input
                          type="text"
                          value={subtask.name}
                          onChange={(e) => updateSubtask(index, "name", e.target.value)}
                          className="w-full px-3 py-2 text-sm border border-slate-300 dark:border-slate-600 rounded focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                          placeholder="Subtask name"
                        />
                        <textarea
                          value={subtask.description}
                          onChange={(e) => updateSubtask(index, "description", e.target.value)}
                          rows={2}
                          className="w-full px-3 py-2 text-sm border border-slate-300 dark:border-slate-600 rounded focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                          placeholder="Subtask description"
                        />
                        <div className="flex space-x-2">
                          <input
                            type="number"
                            min="0"
                            value={subtask.estimatedHours}
                            onChange={(e) => updateSubtask(index, "estimatedHours", parseInt(e.target.value) || 0)}
                            className="w-20 px-2 py-1 text-sm border border-slate-300 dark:border-slate-600 rounded focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                            placeholder="Hours"
                          />
                          <input
                            type="number"
                            min="0"
                            max="59"
                            value={subtask.estimatedMinutes}
                            onChange={(e) => updateSubtask(index, "estimatedMinutes", parseInt(e.target.value) || 0)}
                            className="w-20 px-2 py-1 text-sm border border-slate-300 dark:border-slate-600 rounded focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                            placeholder="Mins"
                          />
                          <span className="text-xs text-slate-500 dark:text-slate-400 self-center">
                            {formatTime(subtask.estimatedHours * 60 + subtask.estimatedMinutes)}
                          </span>
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeSubtask(index)}
                        className="ml-2 text-red-500 hover:text-red-700"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Add New Subtask */}
            <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg border-2 border-dashed border-slate-300 dark:border-slate-600">
              <div className="space-y-3">
                <input
                  type="text"
                  value={newSubtask.name}
                  onChange={(e) => handleSubtaskChange("name", e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-slate-300 dark:border-slate-600 rounded focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                  placeholder="Add subtask name"
                />
                <textarea
                  value={newSubtask.description}
                  onChange={(e) => handleSubtaskChange("description", e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 text-sm border border-slate-300 dark:border-slate-600 rounded focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                  placeholder="Add subtask description"
                />
                <div className="flex items-center justify-between">
                  <div className="flex space-x-2">
                    <input
                      type="number"
                      min="0"
                      value={newSubtask.estimatedHours}
                      onChange={(e) => handleSubtaskChange("estimatedHours", parseInt(e.target.value) || 0)}
                      className="w-20 px-2 py-1 text-sm border border-slate-300 dark:border-slate-600 rounded focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                      placeholder="Hours"
                    />
                    <input
                      type="number"
                      min="0"
                      max="59"
                      value={newSubtask.estimatedMinutes}
                      onChange={(e) => handleSubtaskChange("estimatedMinutes", parseInt(e.target.value) || 0)}
                      className="w-20 px-2 py-1 text-sm border border-slate-300 dark:border-slate-600 rounded focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                      placeholder="Mins"
                    />
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addSubtask}
                    disabled={!newSubtask.name.trim()}
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Add Subtask
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-slate-200 dark:border-slate-700">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={loading || !formData.name.trim()}
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {task ? "Updating..." : "Creating..."}
                </>
              ) : (
                task ? "Update Task" : "Create Task"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
