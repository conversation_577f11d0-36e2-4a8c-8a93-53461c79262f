import axios from 'axios';

const API_BASE = 'http://localhost:5000/api';

// Test data
let adminToken = '';
let employeeToken = '';
let projectId = '';
let taskId = '';

async function testProjectTaskAPI() {
  console.log('🚀 Starting Project Task API Test...\n');

  try {
    // Step 1: Login as admin to create a project
    console.log('1. 📝 Admin Login...');
    const adminLogin = await axios.post(`${API_BASE}/auth/admin/login`, {
      username: 'testadmin_project',
      password: 'TestAdmin123!'
    });
    
    adminToken = adminLogin.data.token;
    console.log('✅ Admin logged in successfully');

    // Step 2: Create a project
    console.log('\n2. 🏗️ Creating a project...');
    const projectData = {
      name: 'Test Project for Tasks',
      description: 'A test project to verify task management functionality',
      projectType: 'public',
      priority: 'medium',
      assignment: [
        {
          employee: '676b5b8b8b8b8b8b8b8b8b8b', // This should be a real employee ID
          department: '676b5b8b8b8b8b8b8b8b8b8b' // This should be a real department ID
        }
      ]
    };

    try {
      const projectResponse = await axios.post(`${API_BASE}/project/create`, projectData, {
        headers: { Authorization: `Bearer ${adminToken}` }
      });
      projectId = projectResponse.data.project._id;
      console.log('✅ Project created:', projectResponse.data.project.name);
    } catch (error) {
      console.log('⚠️ Project creation failed (might already exist), continuing with existing project...');
      // Try to get existing projects
      const projectsResponse = await axios.get(`${API_BASE}/project/all`, {
        headers: { Authorization: `Bearer ${adminToken}` }
      });
      if (projectsResponse.data.data.length > 0) {
        projectId = projectsResponse.data.data[0]._id;
        console.log('✅ Using existing project:', projectsResponse.data.data[0].name);
      }
    }

    // Step 3: Login as employee
    console.log('\n3. 👤 Employee Login...');
    try {
      const employeeLogin = await axios.post(`${API_BASE}/auth/employee/login`, {
        username: 'anuk',
        password: 'anuk123'
      });
      employeeToken = employeeLogin.data.token;
      console.log('✅ Employee logged in successfully');
    } catch (error) {
      console.log('❌ Employee login failed. Please ensure an employee exists with username "testemployee_project" and password "TestEmployee123!"');
      console.log('Error:', error.response?.data?.message || error.message);
      return;
    }

    // Step 4: Get assigned projects for employee
    console.log('\n4. 📋 Getting assigned projects...');
    try {
      const assignedProjects = await axios.get(`${API_BASE}/project/assigned`, {
        headers: { Authorization: `Bearer ${employeeToken}` }
      });
      console.log('✅ Assigned projects retrieved:', assignedProjects.data.count, 'projects');
      
      if (assignedProjects.data.data.length > 0) {
        projectId = assignedProjects.data.data[0]._id;
        console.log('✅ Using assigned project:', assignedProjects.data.data[0].name);
      }
    } catch (error) {
      console.log('❌ Failed to get assigned projects:', error.response?.data?.message || error.message);
      return;
    }

    // Step 5: Create a project task
    console.log('\n5. ✏️ Creating a project task...');
    const taskData = {
      name: 'Test Task',
      description: 'This is a test task created via API',
      projectId: projectId,
      estimatedHours: 2,
      estimatedMinutes: 30,
      subtasks: [
        {
          name: 'Subtask 1',
          description: 'First subtask',
          estimatedHours: 1,
          estimatedMinutes: 0
        },
        {
          name: 'Subtask 2',
          description: 'Second subtask',
          estimatedHours: 0,
          estimatedMinutes: 45
        }
      ]
    };

    try {
      const taskResponse = await axios.post(`${API_BASE}/project-task/create`, taskData, {
        headers: { Authorization: `Bearer ${employeeToken}` }
      });
      taskId = taskResponse.data.data._id;
      console.log('✅ Task created successfully:', taskResponse.data.data.name);
      console.log('   - Subtasks:', taskResponse.data.data.subtasks.length);
    } catch (error) {
      console.log('❌ Task creation failed:', error.response?.data?.message || error.message);
      return;
    }

    // Step 6: Get my tasks
    console.log('\n6. 📝 Getting my tasks...');
    try {
      const myTasks = await axios.get(`${API_BASE}/project-task/my-tasks`, {
        headers: { Authorization: `Bearer ${employeeToken}` }
      });
      console.log('✅ My tasks retrieved:', myTasks.data.data.length, 'tasks');
    } catch (error) {
      console.log('❌ Failed to get my tasks:', error.response?.data?.message || error.message);
    }

    // Step 7: Get tasks for the project
    console.log('\n7. 📋 Getting project tasks...');
    try {
      const projectTasks = await axios.get(`${API_BASE}/project-task/project/${projectId}`, {
        headers: { Authorization: `Bearer ${employeeToken}` }
      });
      console.log('✅ Project tasks retrieved:', projectTasks.data.data.length, 'tasks');
    } catch (error) {
      console.log('❌ Failed to get project tasks:', error.response?.data?.message || error.message);
    }

    // Step 8: Update task status
    console.log('\n8. 🔄 Updating task status...');
    try {
      const updateResponse = await axios.put(`${API_BASE}/project-task/${taskId}`, {
        status: 'in progress'
      }, {
        headers: { Authorization: `Bearer ${employeeToken}` }
      });
      console.log('✅ Task status updated to:', updateResponse.data.data.status);
    } catch (error) {
      console.log('❌ Failed to update task:', error.response?.data?.message || error.message);
    }

    // Step 9: Add a subtask
    console.log('\n9. ➕ Adding a subtask...');
    try {
      const subtaskResponse = await axios.post(`${API_BASE}/project-task/${taskId}/subtasks`, {
        name: 'New Subtask',
        description: 'Added via API',
        estimatedHours: 0,
        estimatedMinutes: 30
      }, {
        headers: { Authorization: `Bearer ${employeeToken}` }
      });
      console.log('✅ Subtask added successfully');
      console.log('   - Total subtasks now:', subtaskResponse.data.data.subtasks.length);
    } catch (error) {
      console.log('❌ Failed to add subtask:', error.response?.data?.message || error.message);
    }

    // Step 10: Get task details
    console.log('\n10. 🔍 Getting task details...');
    try {
      const taskDetails = await axios.get(`${API_BASE}/project-task/${taskId}`, {
        headers: { Authorization: `Bearer ${employeeToken}` }
      });
      console.log('✅ Task details retrieved:');
      console.log('   - Name:', taskDetails.data.data.name);
      console.log('   - Status:', taskDetails.data.data.status);
      console.log('   - Progress:', taskDetails.data.data.progressPercentage + '%');
      console.log('   - Subtasks:', taskDetails.data.data.subtasks.length);
    } catch (error) {
      console.log('❌ Failed to get task details:', error.response?.data?.message || error.message);
    }

    console.log('\n🎉 Project Task API Test Completed Successfully!\n');
    
    console.log('📋 Test Summary:');
    console.log('✅ Employee authentication');
    console.log('✅ Project assignment retrieval');
    console.log('✅ Task creation with subtasks');
    console.log('✅ Task listing and filtering');
    console.log('✅ Task status updates');
    console.log('✅ Subtask management');
    console.log('✅ Task detail retrieval');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testProjectTaskAPI();
