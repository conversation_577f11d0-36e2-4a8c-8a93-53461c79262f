import mongoose from "mongoose"

const subtaskSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    description: {
      type: String,
      trim: true,
      maxlength: 500,
    },
    status: {
      type: String,
      enum: ["to do", "in progress", "on hold", "completed", "cancelled"],
      default: "to do",
    },
    estimatedHours: {
      type: Number,
      min: 0,
      default: 0,
    },
    estimatedMinutes: {
      type: Number,
      min: 0,
      max: 59,
      default: 0,
    },
    completedAt: {
      type: Date,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Employee",
      required: true,
    },
  },
  {
    timestamps: true,
  }
)

const projectTaskSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    description: {
      type: String,
      trim: true,
      maxlength: 1000,
    },
    project: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Project",
      required: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Employee",
      required: true,
    },
    status: {
      type: String,
      enum: ["to do", "in progress", "on hold", "completed", "cancelled"],
      default: "to do",
    },
    estimatedHours: {
      type: Number,
      min: 0,
      default: 0,
    },
    estimatedMinutes: {
      type: Number,
      min: 0,
      max: 59,
      default: 0,
    },
    subtasks: [subtaskSchema],
    completedAt: {
      type: Date,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
)

// Index for better query performance
projectTaskSchema.index({ project: 1, createdBy: 1 })
projectTaskSchema.index({ status: 1, createdAt: -1 })
projectTaskSchema.index({ createdBy: 1, status: 1 })

// Virtual for total estimated time in minutes
projectTaskSchema.virtual("totalEstimatedMinutes").get(function () {
  const taskMinutes = this.estimatedHours * 60 + this.estimatedMinutes
  const subtaskMinutes = this.subtasks.reduce((total, subtask) => {
    return total + (subtask.estimatedHours * 60 + subtask.estimatedMinutes)
  }, 0)
  return taskMinutes + subtaskMinutes
})

// Virtual for completed subtasks count
projectTaskSchema.virtual("completedSubtasksCount").get(function () {
  return this.subtasks.filter(subtask => subtask.status === "completed").length
})

// Virtual for task progress percentage
projectTaskSchema.virtual("progressPercentage").get(function () {
  if (this.status === "completed") return 100
  if (this.status === "cancelled") return 0
  if (this.subtasks.length === 0) {
    return this.status === "in progress" ? 50 : 0
  }
  
  const completedSubtasks = this.completedSubtasksCount
  return Math.round((completedSubtasks / this.subtasks.length) * 100)
})

// Method to add subtask
projectTaskSchema.methods.addSubtask = function (subtaskData, employeeId) {
  this.subtasks.push({
    ...subtaskData,
    createdBy: employeeId,
  })
  return this.save()
}

// Method to update subtask
projectTaskSchema.methods.updateSubtask = function (subtaskId, updateData) {
  const subtask = this.subtasks.id(subtaskId)
  if (!subtask) {
    throw new Error("Subtask not found")
  }
  
  Object.keys(updateData).forEach(key => {
    subtask[key] = updateData[key]
  })
  
  if (updateData.status === "completed" && !subtask.completedAt) {
    subtask.completedAt = new Date()
  } else if (updateData.status !== "completed" && subtask.completedAt) {
    subtask.completedAt = undefined
  }
  
  return this.save()
}

// Method to remove subtask
projectTaskSchema.methods.removeSubtask = function (subtaskId) {
  this.subtasks.pull(subtaskId)
  return this.save()
}

// Method to mark task as completed
projectTaskSchema.methods.markCompleted = function () {
  this.status = "completed"
  this.completedAt = new Date()
  
  // Mark all subtasks as completed
  this.subtasks.forEach(subtask => {
    if (subtask.status !== "completed") {
      subtask.status = "completed"
      subtask.completedAt = new Date()
    }
  })
  
  return this.save()
}

// Pre-save middleware to handle status changes
projectTaskSchema.pre("save", function (next) {
  if (this.isModified("status")) {
    if (this.status === "completed" && !this.completedAt) {
      this.completedAt = new Date()
    } else if (this.status !== "completed" && this.completedAt) {
      this.completedAt = undefined
    }
  }
  next()
})

// Ensure virtuals are included in JSON output
projectTaskSchema.set("toJSON", { virtuals: true })
projectTaskSchema.set("toObject", { virtuals: true })

export default mongoose.model("ProjectTask", projectTaskSchema)
