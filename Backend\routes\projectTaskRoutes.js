import express from "express"
import {
  createProjectTask,
  getProjectTasks,
  getMyProjectTasks,
  getProjectTaskById,
  updateProjectTask,
  deleteProjectTask,
  addSubtask,
  updateSubtask,
  deleteSubtask,
} from "../controllers/projectTaskController.js"
import { verifyToken, employeeOnly } from "../middleware/auth.js"

const router = express.Router()

// Project task CRUD operations (Employee only)
router.post("/create", verifyToken, employeeOnly, createProjectTask)
router.get("/my-tasks", verifyToken, employeeOnly, getMyProjectTasks)
router.get("/project/:projectId", verifyToken, employeeOnly, getProjectTasks)
router.get("/:taskId", verifyToken, employeeOnly, getProjectTaskById)
router.put("/:taskId", verifyToken, employeeOnly, updateProjectTask)
router.delete("/:taskId", verifyToken, employeeOnly, deleteProjectTask)

// Subtask operations (Employee only)
router.post("/:taskId/subtasks", verifyToken, employeeOnly, addSubtask)
router.put("/:taskId/subtasks/:subtaskId", verifyToken, employeeOnly, updateSubtask)
router.delete("/:taskId/subtasks/:subtaskId", verifyToken, employeeOnly, deleteSubtask)

export default router
