import { useState } from "react"
import { <PERSON>, Card<PERSON>eader, CardTitle, CardDescription, CardContent } from "./ui/Card"
import { Button } from "./ui/Button"
import { Badge } from "./ui/Badge"
import { projectTaskService } from "../services/projectService"

export default function TaskDetail({ task, onEdit, onDelete, onBack, onTaskUpdate }) {
  const [loading, setLoading] = useState(false)
  const [updatingSubtask, setUpdatingSubtask] = useState(null)

  const getStatusColor = (status) => {
    switch (status) {
      case "completed": return "success"
      case "in progress": return "warning"
      case "to do": return "default"
      case "on hold": return "secondary"
      case "cancelled": return "destructive"
      default: return "default"
    }
  }

  const formatTime = (hours, minutes) => {
    if (hours === 0 && minutes === 0) return "No estimate"
    if (hours === 0) return `${minutes}m`
    if (minutes === 0) return `${hours}h`
    return `${hours}h ${minutes}m`
  }

  const handleStatusChange = async (newStatus) => {
    try {
      setLoading(true)
      const response = await projectTaskService.updateTask(task._id, { status: newStatus })
      if (onTaskUpdate) onTaskUpdate(response.data)
    } catch (err) {
      console.error("Error updating task status:", err)
      alert("Failed to update task status. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  const handleSubtaskStatusChange = async (subtaskId, newStatus) => {
    try {
      setUpdatingSubtask(subtaskId)
      const response = await projectTaskService.updateSubtask(task._id, subtaskId, { status: newStatus })
      if (onTaskUpdate) onTaskUpdate(response.data)
    } catch (err) {
      console.error("Error updating subtask status:", err)
      alert("Failed to update subtask status. Please try again.")
    } finally {
      setUpdatingSubtask(null)
    }
  }

  const statusOptions = [
    { value: "to do", label: "To Do", color: "default" },
    { value: "in progress", label: "In Progress", color: "warning" },
    { value: "on hold", label: "On Hold", color: "secondary" },
    { value: "completed", label: "Completed", color: "success" },
    { value: "cancelled", label: "Cancelled", color: "destructive" }
  ]

  return (
    <Card variant="modern" className="max-w-4xl mx-auto">
      <CardHeader className="bg-gradient-to-r from-slate-50 to-indigo-50 dark:from-slate-800 dark:to-indigo-900/20 border-b border-slate-200 dark:border-slate-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back
            </Button>
            <div>
              <CardTitle gradient className="text-2xl">{task.name}</CardTitle>
              <CardDescription>
                Project: {task.project?.name} • Created: {new Date(task.createdAt).toLocaleDateString()}
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => onEdit(task)}>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Edit
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => onDelete(task._id)}
              className="text-red-500 hover:text-red-700"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Delete
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6 space-y-6">
        {/* Task Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Description</h3>
            <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
              <p className="text-slate-700 dark:text-slate-300">
                {task.description || "No description provided"}
              </p>
            </div>
          </div>
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Status</h4>
              <select
                value={task.status}
                onChange={(e) => handleStatusChange(e.target.value)}
                disabled={loading}
                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-800 text-slate-900 dark:text-white"
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Estimated Time</h4>
              <Badge variant="outline" className="text-sm">
                {formatTime(task.estimatedHours, task.estimatedMinutes)}
              </Badge>
            </div>
            <div>
              <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Progress</h4>
              <div className="flex items-center space-x-2">
                <div className="flex-1 bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                  <div 
                    className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${task.progressPercentage || 0}%` }}
                  ></div>
                </div>
                <span className="text-sm text-slate-600 dark:text-slate-400">
                  {task.progressPercentage || 0}%
                </span>
              </div>
            </div>
            {task.completedAt && (
              <div>
                <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Completed</h4>
                <Badge variant="success" className="text-sm">
                  {new Date(task.completedAt).toLocaleDateString()}
                </Badge>
              </div>
            )}
          </div>
        </div>

        {/* Subtasks */}
        {task.subtasks && task.subtasks.length > 0 && (
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                Subtasks ({task.completedSubtasksCount}/{task.subtasks.length})
              </h3>
              <Badge variant="outline" size="sm">
                {task.subtasks.length} total
              </Badge>
            </div>
            <div className="space-y-3">
              {task.subtasks.map((subtask) => (
                <div 
                  key={subtask._id} 
                  className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="font-medium text-slate-900 dark:text-white">
                          {subtask.name}
                        </h4>
                        <Badge variant={getStatusColor(subtask.status)} size="sm">
                          {subtask.status}
                        </Badge>
                      </div>
                      {subtask.description && (
                        <p className="text-sm text-slate-600 dark:text-slate-400 mb-2">
                          {subtask.description}
                        </p>
                      )}
                      <div className="flex items-center space-x-4 text-xs text-slate-500 dark:text-slate-400">
                        <span>
                          Estimated: {formatTime(subtask.estimatedHours, subtask.estimatedMinutes)}
                        </span>
                        <span>
                          Created: {new Date(subtask.createdAt).toLocaleDateString()}
                        </span>
                        {subtask.completedAt && (
                          <span>
                            Completed: {new Date(subtask.completedAt).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="ml-4">
                      <select
                        value={subtask.status}
                        onChange={(e) => handleSubtaskStatusChange(subtask._id, e.target.value)}
                        disabled={updatingSubtask === subtask._id}
                        className="px-2 py-1 text-sm border border-slate-300 dark:border-slate-600 rounded focus:ring-2 focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                      >
                        {statusOptions.map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Task Metadata */}
        <div className="pt-6 border-t border-slate-200 dark:border-slate-700">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
            <div>
              <h4 className="font-medium text-slate-700 dark:text-slate-300 mb-2">Created By</h4>
              <p className="text-slate-600 dark:text-slate-400">
                {task.createdBy?.name} ({task.createdBy?.email})
              </p>
            </div>
            <div>
              <h4 className="font-medium text-slate-700 dark:text-slate-300 mb-2">Last Updated</h4>
              <p className="text-slate-600 dark:text-slate-400">
                {new Date(task.updatedAt).toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
