import express from "express"
import {
  createProject,
  getAllProjects,
  getProjectById,
  updateProject,
  deleteProject,
  getEmployeesByDepartment,
  getEmployeeAssignedProjects,
} from "../controllers/projectController.js"
import { verifyToken, adminOrSuperAdmin, employeeOnly } from "../middleware/auth.js"



const router = express.Router()

// Employee endpoints (must come before parameterized routes)
router.get("/assigned", verifyToken, employeeOnly, getEmployeeAssignedProjects)

// Project CRUD operations (Admin and SuperAdmin only)
router.post("/create", verifyToken, adminOrSuperAdmin, createProject)
router.get("/all", verifyToken, adminOrSuperAdmin, getAllProjects)
router.get("/:projectId", verifyToken, adminOrSuperAdmin, getProjectById)
router.put("/:projectId", verifyToken, adminOrSuperAdmin, updateProject)
router.delete("/:projectId", verifyToken, adminOrSuperAdmin, deleteProject)

// Helper endpoints for project assignment
router.get("/employees/department/:departmentId", verifyToken, adminOrSuperAdmin, getEmployeesByDepartment)

export default router
